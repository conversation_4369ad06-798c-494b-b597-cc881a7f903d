# API接口测试数据补充完善文档

## 📋 **文档概述**
**文档目标**: 为284个API接口提供完整的请求参数和业务状态码响应示例代码
**执行标准**: 严格按照 apitest-url.mdc 的顺序连续性补全
**响应格式**: 基于 Controller.php 的 successResponse 和 errorResponse 方法
**状态码体系**: 遵循 ApiCodeEnum.php 中定义的完整业务状态码

---

# 🚀 **第一阶段：用户注册登录** (10个接口)

## 5.1 生成验证码 `GET /api/captcha/generate`

### 请求参数示例
```json
{
    "type": "sms",
    "phone": "13800138000"
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "验证码生成成功",
    "data": {
        "captcha_id": "captcha_abc123def456",
        "expires_in": 300,
        "send_to": "138****8000",
        "type": "sms",
        "can_resend_after": 60
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 422 - 参数验证失败
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "phone": ["手机号格式不正确"],
            "type": ["验证码类型无效"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1052 - 验证码发送频率限制
```json
{
    "code": 1052,
    "message": "验证码发送过于频繁，请稍后再试",
    "data": {
        "retry_after": 45,
        "max_attempts": 5,
        "remaining_attempts": 3
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 5.2 验证验证码 `POST /api/captcha/verify`

### 请求参数示例
```json
{
    "captcha_id": "captcha_abc123def456",
    "code": "123456"
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "验证码验证成功",
    "data": {
        "captcha_id": "captcha_abc123def456",
        "verified": true,
        "verified_at": "2024-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 422 - 参数验证失败
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "captcha_id": ["验证码ID不能为空"],
            "code": ["验证码不能为空"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1050 - 验证码错误
```json
{
    "code": 1050,
    "message": "验证码错误",
    "data": {
        "remaining_attempts": 2,
        "max_attempts": 3,
        "locked_until": null
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1051 - 验证码已过期
```json
{
    "code": 1051,
    "message": "验证码已过期",
    "data": {
        "expired_at": "2024-01-01 11:55:00",
        "can_regenerate": true
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 5.3 刷新验证码 `POST /api/captcha/refresh`

### 请求参数示例
```json
{
    "captcha_id": "captcha_abc123def456"
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "验证码刷新成功",
    "data": {
        "captcha_id": "captcha_new789xyz012",
        "expires_in": 300,
        "old_captcha_id": "captcha_abc123def456",
        "refreshed_at": "2024-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 404 - 验证码不存在
```json
{
    "code": 404,
    "message": "验证码不存在",
    "data": {
        "captcha_id": "captcha_abc123def456",
        "possible_reasons": ["已过期", "已使用", "不存在"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 422 - 参数验证失败
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "captcha_id": ["验证码ID格式不正确"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 4.1 用户注册 `POST /api/register`

### 请求参数示例
```json
{
    "username": "testuser",
    "password": "password123",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "verification_code": "123456",
    "invite_code": "INV123456",
    "agree_terms": true
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "phone": "138****8000",
        "status": "active",
        "created_at": "2024-01-01 12:00:00",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires_in": 2592000
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 422 - 参数验证失败
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "username": ["用户名已存在"],
            "password": ["密码长度至少6位"],
            "email": ["邮箱格式不正确"],
            "phone": ["手机号格式不正确"],
            "verification_code": ["验证码错误"],
            "agree_terms": ["必须同意服务条款"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1002 - 用户已存在
```json
{
    "code": 1002,
    "message": "用户已存在",
    "data": {
        "username": "testuser",
        "conflict_field": "username",
        "suggestion": "请尝试其他用户名"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1009 - 邮箱已存在
```json
{
    "code": 1009,
    "message": "邮箱已存在",
    "data": {
        "email": "<EMAIL>",
        "conflict_field": "email",
        "can_login": true
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 4.2 用户登录 `POST /api/login`

### 请求参数示例
```json
{
    "login": "testuser",
    "password": "password123",
    "device_info": {
        "device_type": "web",
        "device_name": "Chrome Browser",
        "ip_address": "*************",
        "user_agent": "Mozilla/5.0..."
    }
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": "refresh_abc123def456",
        "expires_in": 2592000,
        "user_info": {
            "avatar": "https://example.com/avatar.jpg",
            "nickname": "测试用户",
            "level": 1,
            "points": 100
        },
        "login_time": "2024-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1001 - 用户名或密码错误
```json
{
    "code": 1001,
    "message": "用户名或密码错误",
    "data": {
        "remaining_attempts": 2,
        "max_attempts": 5,
        "locked_until": null
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1003 - 账户被锁定
```json
{
    "code": 1003,
    "message": "账户被锁定",
    "data": {
        "locked_until": "2024-01-01 13:00:00",
        "lock_reason": "多次登录失败",
        "unlock_methods": ["等待解锁", "联系客服"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 4.7 验证Token `GET /api/verify`

### 请求参数示例
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "Token验证成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "token_valid": true,
        "expires_at": "2024-02-01 12:00:00",
        "remaining_time": 2591999,
        "permissions": ["read", "write", "upload"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - Token无效或过期
```json
{
    "code": 401,
    "message": "Token无效或过期",
    "data": {
        "token_status": "expired",
        "expired_at": "2024-01-01 11:00:00",
        "need_refresh": true
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 4.4 刷新Token `POST /api/refresh`

### 请求参数示例
```json
{
    "refresh_token": "refresh_abc123def456"
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "Token刷新成功",
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": "refresh_new789xyz012",
        "expires_in": 2592000,
        "token_type": "Bearer",
        "issued_at": "2024-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - refresh_token无效或过期
```json
{
    "code": 401,
    "message": "refresh_token无效或过期",
    "data": {
        "refresh_token_status": "expired",
        "need_relogin": true
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 422 - 参数验证失败
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "refresh_token": ["refresh_token不能为空"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 4.3 用户登出 `POST /api/logout`

### 请求参数示例
```json
{
    "all_devices": false
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "登出成功",
    "data": {
        "logout_time": "2024-01-01 12:00:00",
        "devices_logged_out": 1,
        "all_devices": false
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 4.5 忘记密码 `POST /api/forgot-password`

### 请求参数示例
```json
{
    "email": "<EMAIL>"
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "密码重置邮件发送成功",
    "data": {
        "email": "<EMAIL>",
        "reset_token_expires": "2024-01-01 13:00:00",
        "sent_at": "2024-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 404 - 用户不存在
```json
{
    "code": 404,
    "message": "用户不存在",
    "data": {
        "email": "<EMAIL>",
        "suggestion": "请检查邮箱地址或注册新账户"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 422 - 参数验证失败
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "email": ["邮箱格式不正确"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 4.6 重置密码 `POST /api/reset-password`

### 请求参数示例
```json
{
    "token": "reset_token_abc123def456",
    "new_password": "newpassword123",
    "password_confirmation": "newpassword123"
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "密码重置成功",
    "data": {
        "user_id": 12345,
        "reset_at": "2024-01-01 12:00:00",
        "auto_login": false
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 422 - 参数验证失败
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "new_password": ["密码长度至少6位"],
            "password_confirmation": ["两次密码输入不一致"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1004 - 重置token无效或过期
```json
{
    "code": 1004,
    "message": "重置token无效或过期",
    "data": {
        "token_status": "expired",
        "expired_at": "2024-01-01 11:00:00",
        "need_request_new": true
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

# 🔐 **第二阶段：用户安全验证** (4个接口)

## 24.1 用户中心信息 `GET /api/user/profile`

### 请求参数示例
```json
{}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "user_id": 12345,
        "username": "testuser",
        "email": "<EMAIL>",
        "phone": "138****8000",
        "avatar": "https://example.com/avatar.jpg",
        "nickname": "测试用户",
        "level": 1,
        "points": 100,
        "credits": 500,
        "status": "active",
        "created_at": "2024-01-01 10:00:00",
        "last_login": "2024-01-01 12:00:00",
        "profile_completion": 85
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 23.1 获取用户权限 `GET /api/permissions/user`

### 请求参数示例
```json
{
    "user_id": 12345
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "user_id": 12345,
        "permissions": [
            {
                "permission": "user.read",
                "description": "查看用户信息",
                "granted_at": "2024-01-01 10:00:00"
            },
            {
                "permission": "content.create",
                "description": "创建内容",
                "granted_at": "2024-01-01 10:00:00"
            },
            {
                "permission": "ai.generate",
                "description": "AI生成功能",
                "granted_at": "2024-01-01 10:00:00"
            }
        ],
        "roles": [
            {
                "role_id": 1,
                "role_name": "普通用户",
                "assigned_at": "2024-01-01 10:00:00"
            }
        ]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 403 - 无权限
```json
{
    "code": 403,
    "message": "无权限访问",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 404 - 用户不存在
```json
{
    "code": 404,
    "message": "用户不存在",
    "data": {
        "user_id": 12345
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 23.2 检查用户权限 `POST /api/permissions/check`

### 请求参数示例
```json
{
    "permission": "ai.generate",
    "resource_id": "project_123"
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "权限检查结果",
    "data": {
        "permission": "ai.generate",
        "resource_id": "project_123",
        "has_permission": true,
        "granted_by": "role",
        "expires_at": null,
        "checked_at": "2024-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 24.4 获取用户偏好设置 `GET /api/user/preferences`

### 请求参数示例
```json
{}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "user_id": 12345,
        "preferences": {
            "language": "zh-CN",
            "theme": "light",
            "notifications": {
                "email": true,
                "sms": false,
                "push": true
            },
            "ai_settings": {
                "preferred_platform": "LiblibAI",
                "auto_fallback": true,
                "quality_preference": "high"
            },
            "privacy": {
                "profile_public": false,
                "works_public": true,
                "allow_follow": true
            }
        },
        "updated_at": "2024-01-01 11:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

# 🌐 **第三阶段：WebSocket安全验证** (2个接口)

## 7.4 WebSocket服务状态 `GET /api/websocket/status`

### 请求参数示例
```json
{}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "service_status": "running",
        "server_info": {
            "host": "localhost",
            "port": 8080,
            "protocol": "wss",
            "version": "1.0.0"
        },
        "connection_stats": {
            "active_connections": 25,
            "max_connections": 1000,
            "total_connections": 1250
        },
        "uptime": 86400,
        "last_restart": "2024-01-01 00:00:00",
        "health_check": "passed"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1012 - 服务不可用
```json
{
    "code": 1012,
    "message": "WebSocket服务不可用",
    "data": {
        "service_status": "down",
        "error_reason": "服务器维护中",
        "estimated_recovery": "2024-01-01 14:00:00",
        "alternative_methods": ["HTTP轮询"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## 7.1 WebSocket连接认证 `POST /api/websocket/auth`

### 请求参数示例
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "channel": "user_12345"
}
```

### 响应示例

#### 200 - 成功
```json
{
    "code": 200,
    "message": "认证成功",
    "data": {
        "auth_token": "ws_auth_abc123def456",
        "channel": "user_12345",
        "expires_in": 3600,
        "connection_id": "conn_789xyz012",
        "allowed_events": [
            "task.progress",
            "ai.generation.status",
            "notification.new"
        ],
        "server_endpoint": "wss://api.tiptop.cn:8080"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 401 - 认证失败
```json
{
    "code": 401,
    "message": "认证失败",
    "data": {
        "error_type": "invalid_token",
        "token_status": "expired",
        "need_refresh": true
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1001 - TOKEN无效
```json
{
    "code": 1001,
    "message": "TOKEN无效",
    "data": {
        "token_error": "malformed",
        "provided_token": "eyJ0eXAiOiJKV1Q...",
        "need_relogin": true
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

# 📊 **第四阶段：基础数据接口** (39个接口)

## **4.1 多模型选择** (7个接口)

### 2.1 获取可用模型 `GET /api/ai-models/available`

#### 请求参数示例
```json
{
    "type": "image_generation",
    "capability": "high_quality",
    "page": 1
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "models": [
            {
                "model_id": "liblib_ai_v1",
                "model_name": "LiblibAI 图像生成",
                "platform": "LiblibAI",
                "type": "image_generation",
                "capabilities": ["high_quality", "fast_generation"],
                "status": "available",
                "cost_per_use": 10,
                "max_resolution": "2048x2048",
                "supported_styles": ["realistic", "anime", "artistic"]
            },
            {
                "model_id": "kling_ai_v1",
                "model_name": "KlingAI 图像生成",
                "platform": "KlingAI",
                "type": "image_generation",
                "capabilities": ["high_quality", "style_transfer"],
                "status": "available",
                "cost_per_use": 12,
                "max_resolution": "2048x2048",
                "supported_styles": ["realistic", "cartoon", "oil_painting"]
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 15,
            "last_page": 1
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

### 2.2 获取模型详情 `GET /api/ai-models/{model_id}/detail`

#### 请求参数示例
```json
{
    "model_id": "liblib_ai_v1"
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "model_id": "liblib_ai_v1",
        "model_name": "LiblibAI 图像生成",
        "platform": "LiblibAI",
        "version": "1.0.0",
        "type": "image_generation",
        "description": "专业的AI图像生成模型，支持多种艺术风格",
        "capabilities": [
            "high_quality",
            "fast_generation",
            "style_transfer",
            "batch_processing"
        ],
        "specifications": {
            "max_resolution": "2048x2048",
            "supported_formats": ["jpg", "png", "webp"],
            "generation_time": "30-60秒",
            "concurrent_limit": 5
        },
        "pricing": {
            "cost_per_use": 10,
            "currency": "credits",
            "bulk_discount": {
                "10_uses": 0.9,
                "50_uses": 0.8,
                "100_uses": 0.7
            }
        },
        "supported_styles": [
            "realistic",
            "anime",
            "artistic",
            "oil_painting",
            "watercolor"
        ],
        "status": "available",
        "usage_stats": {
            "total_generations": 15420,
            "success_rate": 0.95,
            "average_rating": 4.7
        },
        "created_at": "2024-01-01 00:00:00",
        "updated_at": "2024-01-01 10:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 404 - 模型不存在
```json
{
    "code": 404,
    "message": "模型不存在",
    "data": {
        "model_id": "liblib_ai_v1",
        "available_models": ["kling_ai_v1", "minimax_v1", "deepseek_v1"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

### 2.3 获取收藏模型 `GET /api/ai-models/favorites`

#### 请求参数示例
```json
{
    "page": 1,
    "per_page": 20
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "favorites": [
            {
                "model_id": "liblib_ai_v1",
                "model_name": "LiblibAI 图像生成",
                "platform": "LiblibAI",
                "type": "image_generation",
                "favorited_at": "2024-01-01 10:00:00",
                "usage_count": 25,
                "last_used": "2024-01-01 11:30:00"
            },
            {
                "model_id": "kling_ai_video_v1",
                "model_name": "KlingAI 视频生成",
                "platform": "KlingAI",
                "type": "video_generation",
                "favorited_at": "2024-01-01 09:00:00",
                "usage_count": 12,
                "last_used": "2024-01-01 11:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 5,
            "last_page": 1
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

### 2.4 模型列表 `GET /api/ai-models/list`

#### 请求参数示例
```json
{
    "category": "image_generation",
    "sort": "popularity",
    "page": 1
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "models": [
            {
                "model_id": "liblib_ai_v1",
                "model_name": "LiblibAI 图像生成",
                "platform": "LiblibAI",
                "type": "image_generation",
                "status": "available",
                "popularity_score": 95,
                "cost_per_use": 10
            },
            {
                "model_id": "kling_ai_v1",
                "model_name": "KlingAI 图像生成",
                "platform": "KlingAI",
                "type": "image_generation",
                "status": "available",
                "popularity_score": 88,
                "cost_per_use": 12
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 15,
            "last_page": 1
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

### 2.5 智能平台切换 `POST /api/ai-models/switch`

#### 请求参数示例
```json
{
    "business_type": "image_generation",
    "preferred_platform": "LiblibAI",
    "fallback_enabled": true,
    "task_config": {
        "quality": "high",
        "style": "realistic",
        "resolution": "1024x1024"
    }
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "平台切换成功",
    "data": {
        "selected_platform": "LiblibAI",
        "model_id": "liblib_ai_v1",
        "switch_reason": "preferred_platform_available",
        "fallback_platforms": ["KlingAI", "MiniMax"],
        "estimated_cost": 10,
        "estimated_time": "30-60秒"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 404 - 模型不存在
```json
{
    "code": 404,
    "message": "模型不存在",
    "data": {
        "business_type": "image_generation",
        "preferred_platform": "LiblibAI",
        "available_platforms": ["KlingAI", "MiniMax"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 1013 - 模型不可用
```json
{
    "code": 1013,
    "message": "模型不可用",
    "data": {
        "platform": "LiblibAI",
        "unavailable_reason": "maintenance",
        "estimated_recovery": "2024-01-01 14:00:00",
        "fallback_platform": "KlingAI"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

### 2.6 平台性能对比 `GET /api/ai-models/platform-comparison`

#### 请求参数示例
```json
{
    "business_type": "image_generation",
    "comparison_metrics": ["speed", "quality", "cost"],
    "time_range": "7d"
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "comparison_period": "7d",
        "business_type": "image_generation",
        "platforms": [
            {
                "platform": "LiblibAI",
                "metrics": {
                    "average_speed": 45,
                    "quality_score": 9.2,
                    "cost_per_use": 10,
                    "success_rate": 0.95,
                    "user_satisfaction": 4.7
                },
                "rank": 1
            },
            {
                "platform": "KlingAI",
                "metrics": {
                    "average_speed": 52,
                    "quality_score": 8.8,
                    "cost_per_use": 12,
                    "success_rate": 0.92,
                    "user_satisfaction": 4.5
                },
                "rank": 2
            },
            {
                "platform": "MiniMax",
                "metrics": {
                    "average_speed": 38,
                    "quality_score": 8.5,
                    "cost_per_use": 8,
                    "success_rate": 0.90,
                    "user_satisfaction": 4.3
                },
                "rank": 3
            }
        ],
        "recommendation": {
            "best_for_speed": "MiniMax",
            "best_for_quality": "LiblibAI",
            "best_for_cost": "MiniMax",
            "overall_best": "LiblibAI"
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

### 2.7 按业务类型获取可选平台 `GET /api/ai-models/business-platforms`

#### 请求参数示例
```json
{
    "business_type": "image_generation",
    "include_alternatives": true
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "business_type": "image_generation",
        "primary_platforms": [
            {
                "platform": "LiblibAI",
                "model_id": "liblib_ai_v1",
                "status": "available",
                "capabilities": ["high_quality", "fast_generation"],
                "cost_per_use": 10
            },
            {
                "platform": "KlingAI",
                "model_id": "kling_ai_v1",
                "status": "available",
                "capabilities": ["high_quality", "style_transfer"],
                "cost_per_use": 12
            },
            {
                "platform": "MiniMax",
                "model_id": "minimax_v1",
                "status": "available",
                "capabilities": ["fast_generation", "batch_processing"],
                "cost_per_use": 8
            }
        ],
        "alternative_platforms": [
            {
                "platform": "DeepSeek",
                "model_id": "deepseek_v1",
                "status": "limited",
                "capabilities": ["text_to_image"],
                "cost_per_use": 6,
                "limitation": "仅支持文本到图像"
            }
        ],
        "recommended_platform": "LiblibAI",
        "fallback_order": ["KlingAI", "MiniMax", "DeepSeek"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 422 - 业务类型不支持
```json
{
    "code": 422,
    "message": "业务类型不支持",
    "data": {
        "business_type": "unsupported_type",
        "supported_types": [
            "image_generation",
            "video_generation",
            "voice_synthesis",
            "music_generation",
            "sound_generation",
            "text_generation"
        ]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## **4.2 风格** (4个接口)

### 10.1 获取剧情风格列表 `GET /api/styles/list`

#### 请求参数示例
```json
{
    "category": "story",
    "page": 1,
    "per_page": 20
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "styles": [
            {
                "id": 1,
                "name": "科幻未来",
                "description": "充满科技感的未来世界风格",
                "category": "story",
                "tags": ["科幻", "未来", "科技"],
                "preview_image": "https://example.com/style1.jpg",
                "usage_count": 1250,
                "created_at": "2024-01-01 10:00:00"
            },
            {
                "id": 2,
                "name": "古风仙侠",
                "description": "传统中国古风仙侠风格",
                "category": "story",
                "tags": ["古风", "仙侠", "传统"],
                "preview_image": "https://example.com/style2.jpg",
                "usage_count": 980,
                "created_at": "2024-01-01 10:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 25,
            "last_page": 2
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

### 10.2 获取风格详情 `GET /api/styles/{id}`

#### 请求参数示例
```json
{
    "id": 1
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "name": "科幻未来",
        "description": "充满科技感的未来世界风格，适合创作科幻题材的故事",
        "category": "story",
        "tags": ["科幻", "未来", "科技", "太空"],
        "preview_images": [
            "https://example.com/style1_1.jpg",
            "https://example.com/style1_2.jpg",
            "https://example.com/style1_3.jpg"
        ],
        "config": {
            "color_palette": ["#0066CC", "#00CCFF", "#FFFFFF", "#333333"],
            "mood": "futuristic",
            "elements": ["机器人", "太空船", "全息投影", "高科技建筑"],
            "atmosphere": "神秘而充满希望"
        },
        "usage_stats": {
            "total_usage": 1250,
            "success_rate": 0.92,
            "average_rating": 4.6
        },
        "created_by": "system",
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 11:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 404 - 风格不存在
```json
{
    "code": 404,
    "message": "风格不存在",
    "data": {
        "style_id": 1,
        "suggestion": "请检查风格ID或浏览风格列表"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

### 10.3 获取热门风格 `GET /api/styles/popular`

#### 请求参数示例
```json
{
    "limit": 10,
    "period": "7d"
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "period": "7d",
        "popular_styles": [
            {
                "id": 1,
                "name": "科幻未来",
                "category": "story",
                "usage_count": 156,
                "growth_rate": 0.25,
                "rank": 1,
                "preview_image": "https://example.com/style1.jpg"
            },
            {
                "id": 3,
                "name": "现代都市",
                "category": "story",
                "usage_count": 142,
                "growth_rate": 0.18,
                "rank": 2,
                "preview_image": "https://example.com/style3.jpg"
            },
            {
                "id": 2,
                "name": "古风仙侠",
                "category": "story",
                "usage_count": 128,
                "growth_rate": 0.12,
                "rank": 3,
                "preview_image": "https://example.com/style2.jpg"
            }
        ],
        "total_count": 10
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

### 10.4 创建风格 `POST /api/styles/create`

#### 请求参数示例
```json
{
    "name": "赛博朋克",
    "description": "充满霓虹灯和高科技的未来都市风格",
    "category": "story",
    "config": {
        "color_palette": ["#FF0080", "#00FFFF", "#FFFF00", "#000000"],
        "mood": "dark_futuristic",
        "elements": ["霓虹灯", "机械义肢", "虚拟现实", "摩天大楼"],
        "atmosphere": "黑暗而充满活力"
    }
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "风格创建成功",
    "data": {
        "id": 26,
        "name": "赛博朋克",
        "description": "充满霓虹灯和高科技的未来都市风格",
        "category": "story",
        "tags": ["赛博朋克", "未来", "科技", "都市"],
        "config": {
            "color_palette": ["#FF0080", "#00FFFF", "#FFFF00", "#000000"],
            "mood": "dark_futuristic",
            "elements": ["霓虹灯", "机械义肢", "虚拟现实", "摩天大楼"],
            "atmosphere": "黑暗而充满活力"
        },
        "status": "active",
        "created_by": 12345,
        "created_at": "2024-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 422 - 参数验证失败
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "name": ["风格名称已存在"],
            "description": ["描述不能为空"],
            "category": ["分类无效"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

## **4.3 角色** (9个接口)

### 18.1 角色分类列表 `GET /api/characters/categories`

#### 请求参数示例
```json
{
    "parent_id": null,
    "level": 1
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "categories": [
            {
                "id": 1,
                "name": "现代人物",
                "description": "现代社会中的各类人物角色",
                "parent_id": null,
                "level": 1,
                "character_count": 156,
                "subcategories": [
                    {
                        "id": 11,
                        "name": "职场精英",
                        "character_count": 45
                    },
                    {
                        "id": 12,
                        "name": "学生群体",
                        "character_count": 38
                    }
                ]
            },
            {
                "id": 2,
                "name": "古代人物",
                "description": "古代历史中的各类人物角色",
                "parent_id": null,
                "level": 1,
                "character_count": 128,
                "subcategories": [
                    {
                        "id": 21,
                        "name": "皇室贵族",
                        "character_count": 32
                    },
                    {
                        "id": 22,
                        "name": "江湖侠客",
                        "character_count": 41
                    }
                ]
            }
        ],
        "total_categories": 8
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

---

### 18.2 角色列表 `GET /api/characters/list`

#### 请求参数示例
```json
{
    "category_id": 1,
    "search": "医生",
    "page": 1,
    "per_page": 20
}
```

#### 响应示例

##### 200 - 成功
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "characters": [
            {
                "id": 101,
                "name": "李医生",
                "description": "经验丰富的外科医生，温和而专业",
                "category_id": 1,
                "category_name": "现代人物",
                "avatar": "https://example.com/character101.jpg",
                "attributes": {
                    "age": "35-40",
                    "gender": "男",
                    "personality": ["专业", "温和", "负责"],
                    "occupation": "外科医生"
                },
                "usage_count": 245,
                "rating": 4.8,
                "created_at": "2024-01-01 10:00:00"
            },
            {
                "id": 102,
                "name": "王护士",
                "description": "细心负责的护士长，深受患者喜爱",
                "category_id": 1,
                "category_name": "现代人物",
                "avatar": "https://example.com/character102.jpg",
                "attributes": {
                    "age": "28-32",
                    "gender": "女",
                    "personality": ["细心", "温柔", "专业"],
                    "occupation": "护士长"
                },
                "usage_count": 189,
                "rating": 4.6,
                "created_at": "2024-01-01 10:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 45,
            "last_page": 3
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

##### 401 - 未登录
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```